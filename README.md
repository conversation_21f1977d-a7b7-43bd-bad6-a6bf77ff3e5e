# SKU Mapping Tool: A3 Data ↔ Nielsen Data

## 🎯 Overview

This tool intelligently maps Stock Keeping Units (SKUs) between A3 promotional data and Nielsen retail measurement data using two primary matching algorithms:

1. **EAN Code Exact Matching** (100% confidence)
2. **Composite SKU Matching** (95% confidence)

The solution is designed to handle the complexity of matching products across different data formats and naming conventions while maintaining high accuracy and traceability.

## 📁 Project Structure

```
├── sku_mapper.py              # Main mapping logic
├── README.md                  # This documentation
├── a3_distribution_march_2025_Latest.xlsx    # A3 input data
├── CARREFOUR (incl. Drive) Data Pull 3.xlsx  # Nielsen input data
└── SKU_Mapping_A3_Nielsen_2025.xlsx         # Generated output
```

## 🔧 Requirements

```python
pandas>=1.5.0
numpy>=1.20.0
openpyxl>=3.0.0
pathlib
logging
re
typing
```

## 🚀 Quick Start

### Installation
```bash
# Install required packages
pip install pandas numpy openpyxl

# Run the mapping tool
python sku_mapper.py
```

### Usage
```python
from sku_mapper import SkuMapper

# Initialize with your file paths
mapper = SkuMapper(
    a3_file_path="a3_distribution_march_2025_Latest.xlsx",
    nielsen_file_path="CARREFOUR (incl. Drive) Data Pull 3.xlsx"
)

# Run the complete mapping process
mapper.run_mapping_process()
```

## 🧠 Mapping Logic

### 1. EAN Code Exact Matching

**How it works:**
- Extracts individual EAN codes from A3 data (handles multiple EANs separated by underscores)
- Matches directly with Nielsen UPC codes
- Provides 100% confidence matches

**Example:**
```
A3 EAN: "5410228218067_5410228237501"
Nielsen UPC: "5410228218067"
Result: ✅ EXACT MATCH (EAN: 5410228218067)
```

### 2. Composite SKU Matching

**How it works:**
- Creates normalized composite keys using:
  - Manufacturer (normalized)
  - Brand (normalized)
  - Sub-brand (normalized)
  - Pack format (standardized)

**Normalization Rules:**
```python
# Manufacturer normalization
"AB INBEV FRANCE" → "AB-INBEV"
"CARLSBERG GROUP" → "CARLSBERG"

# Brand normalization  
"ABBAYE DE LEFFE" → "LEFFE"
"LA GOUDALE" → "GOUDALE"

# Volume conversion
"25 CL" → "250ML"
"75 CL" → "750ML"
```

**Example:**
```
A3 Composite Key: "AB-INBEV|LEFFE|LEFFE BLONDE|15 BOUTEILLE 250ML"
Nielsen Composite Key: "AB-INBEV|LEFFE|LEFFE BLONDE|X15 BOUTEILLE VERRE 250ML"
Result: ✅ COMPOSITE MATCH (95% confidence)
```

## 📊 Data Structure

### A3 Data Fields (Input)
- `Fabricant`: Manufacturer name
- `Marque`: Brand name
- `Marque fille`: Sub-brand name
- `Conditionnement`: Pack format (e.g., "15 BTL 25 CL")
- `Ean`: EAN codes (multiple separated by underscore)
- `SKU_`: Composite SKU identifier

### Nielsen Data Fields (Input)
- `FABRICANT`: Manufacturer name
- `MARQUE`: Brand name
- `GAMME`: Sub-brand/variant name
- `CONDITIONNEMENT`: Container type (e.g., "BOUTEILLE VERRE")
- `NBR UNITE`: Number of units (e.g., "X15")
- `CTN UNIT`: Unit volume (e.g., "250ML")
- `UPC`: EAN/UPC code

### Output Structure

The tool generates `SKU_Mapping_A3_Nielsen_2025.xlsx` with the following sheets:

#### 1. Mapping_Summary
```
Year | EAN_Matches | Composite_Matches | Total_Matches | Nielsen_Records
2022 |     1,234   |       567        |     1,801     |     2,500
2023 |     1,456   |       678        |     2,134     |     3,200
...
```

#### 2. Mapping_[Year] (e.g., Mapping_2022)
```
A3_SKU_Identifier | A3_Brand | Nielsen_UPC | Match_Type | Confidence_Score | Match_Logic
LEFFE15BTL25CL   | LEFFE    | 5410228... | EAN_EXACT  |      100        | Exact EAN match: 5410228...
CORONA6BTL355ML  | CORONA   | 7501064... | COMPOSITE_SKU |   95         | Composite SKU match: AB-INBEV|CORONA...
```

## 🔍 Key Features

### ✅ Robust EAN Handling
- Handles multiple EANs in single A3 record
- Cleans and validates EAN formats
- Supports various EAN lengths (8+ digits)

### ✅ Intelligent Normalization
- Manufacturer name standardization
- Brand name mapping
- Volume unit conversion (CL ↔ ML)
- Pack format standardization

### ✅ Year-wise Organization
- Maintains Nielsen's year-based structure
- Separate mapping sheets for each year
- Comprehensive summary statistics

### ✅ Match Traceability
- Records exact matching logic used
- Confidence scores for each match
- Detailed match notes for debugging

### ✅ Data Quality Assurance
- Prevents duplicate mappings
- Validates data consistency
- Comprehensive logging and error handling

## 📈 Expected Performance

Based on typical beverage industry data:

- **EAN Exact Matches**: ~70-80% of total SKUs
- **Composite SKU Matches**: ~15-25% of total SKUs
- **Overall Match Rate**: ~90-95% of A3 SKUs
- **Processing Time**: ~2-5 minutes for 100K+ records

## 🛠️ Code Architecture

### Class Structure
```python
class SkuMapper:
    ├── __init__()                    # Initialize with file paths
    ├── load_data()                   # Load Excel files
    ├── normalize_manufacturer()      # Standardize manufacturer names
    ├── normalize_brand()             # Standardize brand names
    ├── normalize_volume()            # Convert CL to ML
    ├── extract_eans_from_a3()        # Parse multiple EANs
    ├── create_composite_sku_key()    # Generate matching keys
    ├── perform_ean_matching()        # EAN-based matching
    ├── perform_composite_sku_matching() # Composite matching
    ├── generate_mapping_output()     # Create Excel output
    └── run_mapping_process()         # Main orchestration
```

### Design Principles
- **Single Responsibility**: Each method has one clear purpose
- **Type Hints**: Full type annotations for better code clarity
- **Error Handling**: Comprehensive exception handling
- **Logging**: Detailed logging for debugging and monitoring
- **Maintainability**: Clean, commented code following PEP 8

## 🔧 Customization

### Adding New Manufacturer Mappings
```python
self.manufacturer_mapping = {
    'AB INBEV FRANCE': 'AB-INBEV',
    'YOUR_NEW_MANUFACTURER': 'NORMALIZED_NAME'
}
```

### Adding New Brand Mappings
```python
self.brand_mapping = {
    'ABBAYE DE LEFFE': 'LEFFE',
    'YOUR_NEW_BRAND': 'NORMALIZED_BRAND'
}
```

### Adjusting Confidence Scores
```python
# In perform_ean_matching()
'confidence_score': 100,  # EAN matches

# In perform_composite_sku_matching()
'confidence_score': 95,   # Composite matches
```

## 🧪 Example Mappings

### Example 1: EAN Exact Match
```
A3 Record:
├── Fabricant: "AB INBEV FRANCE"
├── Marque: "LEFFE"
├── Marque fille: "LEFFE BLONDE"
├── Conditionnement: "15 BTL 25 CL"
└── Ean: "5410228218067_5410228237501"

Nielsen Record:
├── FABRICANT: "AB-INBEV"
├── MARQUE: "ABBAYE DE LEFFE"
├── GAMME: "LEFFE BLONDE"
├── CONDITIONNEMENT: "BOUTEILLE VERRE"
├── NBR UNITE: "X15"
├── CTN UNIT: "250ML"
└── UPC: "5410228218067"

✅ MATCH: EAN_EXACT (100% confidence)
Match Logic: "Exact EAN match: 5410228218067"
```

### Example 2: Composite SKU Match
```
A3 Record:
├── Fabricant: "CARLSBERG GROUP"
├── Marque: "GRIMBERGEN"
├── Marque fille: "GRIMBERGEN BLONDE"
├── Conditionnement: "6 BTL 33 CL"
└── Ean: "3080216018904" (no Nielsen UPC match)

Nielsen Record:
├── FABRICANT: "CARLSBERG"
├── MARQUE: "GRIMBERGEN"
├── GAMME: "GRIMBERGEN BLONDE"
├── CONDITIONNEMENT: "BOUTEILLE VERRE"
├── NBR UNITE: "X6"
├── CTN UNIT: "330ML"
└── UPC: "3080216999999" (different UPC)

Composite Keys:
A3: "CARLSBERG|GRIMBERGEN|GRIMBERGEN BLONDE|6 BOUTEILLE 330ML"
Nielsen: "CARLSBERG|GRIMBERGEN|GRIMBERGEN BLONDE|X6 BOUTEILLE VERRE 330ML"

✅ MATCH: COMPOSITE_SKU (95% confidence)
Match Logic: "Composite SKU match: CARLSBERG|GRIMBERGEN|GRIMBERGEN BLONDE|..."
```

## 🚨 Troubleshooting

### Common Issues

#### 1. File Not Found Error
```python
FileNotFoundError: [Errno 2] No such file or directory: 'filename.xlsx'
```
**Solution**: Ensure file paths are correct and files exist in the working directory.

#### 2. Sheet Not Found Error
```python
ValueError: Worksheet named 'sheet_name' not found
```
**Solution**: Verify sheet names in Excel files match the expected names in the code.

#### 3. Memory Issues with Large Files
```python
MemoryError: Unable to allocate array
```
**Solution**: Process data in chunks or increase available RAM.

#### 4. Low Match Rates
**Possible Causes**:
- Inconsistent manufacturer/brand naming
- Different pack format conventions
- Missing EAN codes

**Solutions**:
- Add more entries to normalization mappings
- Review and adjust composite key creation logic
- Check data quality in source files

### Debug Mode
Enable detailed logging:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📝 Maintenance

### Regular Updates Needed
1. **Manufacturer Mappings**: Add new manufacturers as they appear
2. **Brand Mappings**: Update brand name variations
3. **Pack Format Rules**: Adjust for new packaging types
4. **Volume Conversions**: Handle new volume units

### Performance Monitoring
- Monitor match rates over time
- Track processing time for large datasets
- Review unmatched items for pattern identification

## 🤝 Contributing

When modifying the code:
1. Follow PEP 8 style guidelines
2. Add type hints for new functions
3. Include comprehensive docstrings
4. Add logging for new operations
5. Update this README with changes

## 📞 Support

For issues or questions:
1. Check the troubleshooting section
2. Review log files for error details
3. Verify input data format and quality
4. Test with smaller datasets first

---

**Created by**: Augment Agent
**Date**: 2025-06-16
**Version**: 1.0.0
