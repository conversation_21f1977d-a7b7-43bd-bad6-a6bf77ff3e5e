"""
SKU Mapping Tool for A3 Data and Nielsen Data
============================================

This tool maps SKUs between A3 promotional data and Nielsen retail measurement data
using EAN codes and composite SKU matching algorithms.

Author: Augment Agent
Date: 2025-06-16
"""

import pandas as pd
import re
from typing import Dict, List
from pathlib import Path
import logging

# Configure logging for better debugging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SkuMapper:
    """
    Main class for mapping SKUs between A3 and Nielsen datasets.
    
    This class handles:
    - Data loading and preprocessing
    - EAN-based exact matching
    - Composite SKU matching using normalized attributes
    - Output generation with mapping logic tracking
    """
    
    def __init__(self, a3_file_path: str, nielsen_file_path: str):
        """
        Initialize the SKU mapper with file paths.
        
        Args:
            a3_file_path (str): Path to A3 Excel file
            nielsen_file_path (str): Path to Nielsen Excel file
        """
        self.a3_file_path = Path(a3_file_path)
        self.nielsen_file_path = Path(nielsen_file_path)
        self.a3_data = None
        self.nielsen_data_by_year = {}
        self.mapping_results = {}
        
        # Normalization mappings for consistent matching
        self.manufacturer_mapping = {
            'AB INBEV FRANCE': 'AB-INBEV',
            'CARLSBERG GROUP': 'CARLSBERG',
            'HEINEKEN ENTREPRISE': 'HEINEKEN',
            'BRASSERIE GOUDALE': 'GOUDALE',
            'I.B.B.': 'IBB'
        }
        
        self.brand_mapping = {
            'ABBAYE DE LEFFE': 'LEFFE',
            'LA GOUDALE': 'GOUDALE',
            'BRASSERIE DU PELICAN': 'PELICAN',
            'SECRET DES MOINES': 'SECRET DES MOINES'
        }
        
        # Volume conversion mapping (CL to ML)
        self.volume_pattern = re.compile(r'(\d+)\s*(CL|ML)', re.IGNORECASE)
        
    def load_data(self) -> None:
        """
        Load data from both Excel files.
        
        Loads A3 data from main sheet and Nielsen data from all year sheets.
        """
        logger.info("Loading A3 data...")
        try:
            self.a3_data = pd.read_excel(
                self.a3_file_path, 
                sheet_name='a3_distribution_march_2025'
            )
            logger.info(f"Loaded {len(self.a3_data)} rows from A3 data")
        except Exception as e:
            logger.error(f"Error loading A3 data: {e}")
            raise
            
        logger.info("Loading Nielsen data...")
        try:
            # Load all Nielsen sheets (year-wise data)
            nielsen_sheets = {
                '2022': '1-Table-1',
                '2023': '2-Table-1', 
                '2024': '3-Table-1',
                '2024_2': '4-Table-1',  # Second 2024 dataset
                '2025': '5-Table-1'
            }
            
            for year, sheet_name in nielsen_sheets.items():
                df = pd.read_excel(self.nielsen_file_path, sheet_name=sheet_name, header=8)  # Header is in row 9 (index 8)

                # Clean column names (remove any extra spaces and handle NaN)
                df.columns = [str(col).strip() if pd.notna(col) else f'Col_{i}' for i, col in enumerate(df.columns)]

                # Filter out empty rows and total rows
                # Check if required columns exist
                required_cols = ['FABRICANT', 'MARQUE', 'UPC']
                missing_cols = [col for col in required_cols if col not in df.columns]
                if missing_cols:
                    logger.warning(f"Missing columns in {year}: {missing_cols}")
                    logger.info(f"Available columns: {list(df.columns)}")
                    continue

                # Remove rows where FABRICANT is empty or contains header text
                df = df.dropna(subset=required_cols)
                df = df[
                    df['FABRICANT'].notna() &
                    (df['FABRICANT'] != '') &
                    (df['FABRICANT'] != 'FABRICANT') &
                    (df['FABRICANT'] != 'Markets')
                ]
                
                self.nielsen_data_by_year[year] = df
                logger.info(f"Loaded {len(df)} rows from Nielsen {year} data")
                
        except Exception as e:
            logger.error(f"Error loading Nielsen data: {e}")
            raise
    
    def normalize_manufacturer(self, manufacturer: str) -> str:
        """
        Normalize manufacturer names for consistent matching.
        
        Args:
            manufacturer (str): Original manufacturer name
            
        Returns:
            str: Normalized manufacturer name
        """
        if pd.isna(manufacturer):
            return ''
        
        manufacturer = str(manufacturer).strip().upper()
        return self.manufacturer_mapping.get(manufacturer, manufacturer)
    
    def normalize_brand(self, brand: str) -> str:
        """
        Normalize brand names for consistent matching.
        
        Args:
            brand (str): Original brand name
            
        Returns:
            str: Normalized brand name
        """
        if pd.isna(brand):
            return ''
            
        brand = str(brand).strip().upper()
        return self.brand_mapping.get(brand, brand)
    
    def normalize_volume(self, volume_text: str) -> str:
        """
        Normalize volume measurements (convert CL to ML).
        
        Args:
            volume_text (str): Text containing volume information
            
        Returns:
            str: Normalized volume in ML
        """
        if pd.isna(volume_text):
            return ''
            
        volume_text = str(volume_text).upper()
        
        # Find all volume matches
        matches = self.volume_pattern.findall(volume_text)
        normalized_volumes = []
        
        for amount, unit in matches:
            if unit == 'CL':
                # Convert CL to ML
                ml_amount = int(amount) * 10
                normalized_volumes.append(f"{ml_amount}ML")
            else:
                normalized_volumes.append(f"{amount}ML")
        
        return ' '.join(normalized_volumes) if normalized_volumes else volume_text

    def extract_eans_from_a3(self, ean_string: str) -> List[str]:
        """
        Extract individual EAN codes from A3 EAN field (underscore separated).

        Args:
            ean_string (str): EAN string from A3 data (may contain multiple EANs)

        Returns:
            List[str]: List of individual EAN codes
        """
        if pd.isna(ean_string):
            return []

        ean_string = str(ean_string).strip()
        if not ean_string:
            return []

        # Split by underscore and clean each EAN
        eans = [ean.strip() for ean in ean_string.split('_') if ean.strip()]

        # Remove any non-numeric characters and validate length
        clean_eans = []
        for ean in eans:
            # Keep only digits
            clean_ean = re.sub(r'[^\d]', '', ean)
            if len(clean_ean) >= 8:  # Minimum EAN length
                clean_eans.append(clean_ean)

        return clean_eans

    def create_composite_sku_key(self, row: pd.Series, data_source: str) -> str:
        """
        Create a composite SKU key for matching purposes.

        Args:
            row (pd.Series): Data row from either A3 or Nielsen
            data_source (str): 'a3' or 'nielsen' to determine column mapping

        Returns:
            str: Composite SKU key for matching
        """
        if data_source == 'a3':
            manufacturer = self.normalize_manufacturer(row.get('Fabricant', ''))
            brand = self.normalize_brand(row.get('Marque', ''))
            sub_brand = self.normalize_brand(row.get('Marque fille', ''))
            pack_info = str(row.get('Conditionnement', '')).upper()

        elif data_source == 'nielsen':
            manufacturer = self.normalize_manufacturer(row.get('FABRICANT', ''))
            brand = self.normalize_brand(row.get('MARQUE', ''))
            sub_brand = self.normalize_brand(row.get('GAMME', ''))

            # Construct pack info from Nielsen fields
            container = str(row.get('CONDITIONNEMENT', '')).upper()
            count = str(row.get('NBR UNITE', '')).replace('X', '')
            volume = str(row.get('CTN UNIT', '')).upper()
            pack_info = f"{count} {container} {volume}".strip()

        else:
            raise ValueError("data_source must be 'a3' or 'nielsen'")

        # Normalize pack info
        pack_info = self.normalize_volume(pack_info)

        # Create composite key
        key_parts = [manufacturer, brand, sub_brand, pack_info]
        composite_key = '|'.join([part.strip() for part in key_parts if part.strip()])

        return composite_key

    def perform_ean_matching(self) -> Dict[str, Dict]:
        """
        Perform EAN-based exact matching between A3 and Nielsen data.

        Returns:
            Dict[str, Dict]: Mapping results by year with EAN matches
        """
        logger.info("Starting EAN-based matching...")

        # Create EAN lookup from A3 data
        a3_ean_lookup = {}
        for idx, row in self.a3_data.iterrows():
            eans = self.extract_eans_from_a3(row['Ean'])
            for ean in eans:
                if ean not in a3_ean_lookup:
                    a3_ean_lookup[ean] = []
                a3_ean_lookup[ean].append({
                    'index': idx,
                    'row': row
                })

        logger.info(f"Created EAN lookup with {len(a3_ean_lookup)} unique EANs from A3 data")

        ean_matches = {}

        # Match against each Nielsen year
        for year, nielsen_df in self.nielsen_data_by_year.items():
            year_matches = []

            for idx, nielsen_row in nielsen_df.iterrows():
                nielsen_upc = str(nielsen_row.get('UPC', '')).strip()

                # Clean Nielsen UPC (remove non-digits)
                clean_upc = re.sub(r'[^\d]', '', nielsen_upc)

                if clean_upc and clean_upc in a3_ean_lookup:
                    # Found EAN match!
                    for a3_match in a3_ean_lookup[clean_upc]:
                        match_record = {
                            'a3_index': a3_match['index'],
                            'nielsen_index': idx,
                            'a3_row': a3_match['row'],
                            'nielsen_row': nielsen_row,
                            'match_type': 'EAN_EXACT',
                            'confidence_score': 100,
                            'matching_ean': clean_upc,
                            'match_notes': f'Exact EAN match: {clean_upc}'
                        }
                        year_matches.append(match_record)

            ean_matches[year] = year_matches
            logger.info(f"Found {len(year_matches)} EAN matches for year {year}")

        return ean_matches

    def perform_composite_sku_matching(self, ean_matches: Dict[str, Dict]) -> Dict[str, Dict]:
        """
        Perform composite SKU matching for items not matched by EAN.

        Args:
            ean_matches (Dict[str, Dict]): Results from EAN matching

        Returns:
            Dict[str, Dict]: Combined EAN and composite SKU matches
        """
        logger.info("Starting composite SKU matching...")

        # Get A3 indices that were already matched by EAN
        ean_matched_a3_indices = set()
        for year_matches in ean_matches.values():
            for match in year_matches:
                ean_matched_a3_indices.add(match['a3_index'])

        # Create composite SKU lookup for unmatched A3 items
        a3_composite_lookup = {}
        for idx, row in self.a3_data.iterrows():
            if idx not in ean_matched_a3_indices:  # Only unmatched items
                composite_key = self.create_composite_sku_key(row, 'a3')
                if composite_key not in a3_composite_lookup:
                    a3_composite_lookup[composite_key] = []
                a3_composite_lookup[composite_key].append({
                    'index': idx,
                    'row': row
                })

        logger.info(f"Created composite SKU lookup with {len(a3_composite_lookup)} keys for unmatched A3 items")

        # Combine EAN matches with composite matches
        all_matches = {}

        for year, nielsen_df in self.nielsen_data_by_year.items():
            # Start with EAN matches for this year
            year_matches = ean_matches.get(year, []).copy()

            # Get Nielsen indices already matched by EAN
            ean_matched_nielsen_indices = set()
            for match in year_matches:
                ean_matched_nielsen_indices.add(match['nielsen_index'])

            # Try composite matching for unmatched Nielsen items
            for idx, nielsen_row in nielsen_df.iterrows():
                if idx not in ean_matched_nielsen_indices:  # Only unmatched items
                    composite_key = self.create_composite_sku_key(nielsen_row, 'nielsen')

                    if composite_key in a3_composite_lookup:
                        # Found composite match!
                        for a3_match in a3_composite_lookup[composite_key]:
                            match_record = {
                                'a3_index': a3_match['index'],
                                'nielsen_index': idx,
                                'a3_row': a3_match['row'],
                                'nielsen_row': nielsen_row,
                                'match_type': 'COMPOSITE_SKU',
                                'confidence_score': 95,
                                'matching_key': composite_key,
                                'match_notes': f'Composite SKU match: {composite_key}'
                            }
                            year_matches.append(match_record)

            all_matches[year] = year_matches
            composite_count = len(year_matches) - len(ean_matches.get(year, []))
            logger.info(f"Found {composite_count} additional composite matches for year {year}")

        return all_matches

    def generate_mapping_output(self, all_matches: Dict[str, Dict]) -> None:
        """
        Generate Excel output with mapping results.

        Args:
            all_matches (Dict[str, Dict]): All matching results by year
        """
        logger.info("Generating mapping output...")

        output_file = Path("SKU_Mapping_A3_Nielsen_2025.xlsx")

        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:

            # Summary sheet
            summary_data = []
            total_ean_matches = 0
            total_composite_matches = 0
            total_matches = 0

            for year, matches in all_matches.items():
                ean_count = sum(1 for m in matches if m['match_type'] == 'EAN_EXACT')
                composite_count = sum(1 for m in matches if m['match_type'] == 'COMPOSITE_SKU')
                year_total = len(matches)

                total_ean_matches += ean_count
                total_composite_matches += composite_count
                total_matches += year_total

                summary_data.append({
                    'Year': year,
                    'EAN_Matches': ean_count,
                    'Composite_Matches': composite_count,
                    'Total_Matches': year_total,
                    'Nielsen_Records': len(self.nielsen_data_by_year[year])
                })

            # Add total row
            summary_data.append({
                'Year': 'TOTAL',
                'EAN_Matches': total_ean_matches,
                'Composite_Matches': total_composite_matches,
                'Total_Matches': total_matches,
                'Nielsen_Records': sum(len(df) for df in self.nielsen_data_by_year.values())
            })

            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='Mapping_Summary', index=False)

            # Year-wise mapping sheets
            for year, matches in all_matches.items():
                if not matches:
                    continue

                mapping_data = []
                for match in matches:
                    a3_row = match['a3_row']
                    nielsen_row = match['nielsen_row']

                    mapping_data.append({
                        'A3_SKU_Identifier': a3_row.get('SKU_', ''),
                        'A3_Manufacturer': a3_row.get('Fabricant', ''),
                        'A3_Brand': a3_row.get('Marque', ''),
                        'A3_Sub_Brand': a3_row.get('Marque fille', ''),
                        'A3_Pack_Format': a3_row.get('Conditionnement', ''),
                        'A3_EAN_Codes': a3_row.get('Ean', ''),
                        'Nielsen_UPC': nielsen_row.get('UPC', ''),
                        'Nielsen_Manufacturer': nielsen_row.get('FABRICANT', ''),
                        'Nielsen_Brand': nielsen_row.get('MARQUE', ''),
                        'Nielsen_Sub_Brand': nielsen_row.get('GAMME', ''),
                        'Nielsen_Container': nielsen_row.get('CONDITIONNEMENT', ''),
                        'Nielsen_Count': nielsen_row.get('NBR UNITE', ''),
                        'Nielsen_Volume': nielsen_row.get('CTN UNIT', ''),
                        'Match_Type': match['match_type'],
                        'Confidence_Score': match['confidence_score'],
                        'Match_Logic': match['match_notes']
                    })

                mapping_df = pd.DataFrame(mapping_data)
                sheet_name = f'Mapping_{year}'
                mapping_df.to_excel(writer, sheet_name=sheet_name, index=False)

                logger.info(f"Generated {len(mapping_data)} mappings for year {year}")

        logger.info(f"Mapping output saved to: {output_file}")

        # Print summary statistics
        print("\n" + "="*60)
        print("SKU MAPPING SUMMARY")
        print("="*60)
        print(f"Total A3 Records: {len(self.a3_data)}")
        print(f"Total Nielsen Records: {sum(len(df) for df in self.nielsen_data_by_year.values())}")
        print(f"Total EAN Matches: {total_ean_matches}")
        print(f"Total Composite Matches: {total_composite_matches}")
        print(f"Total Successful Matches: {total_matches}")
        print(f"Overall Match Rate: {(total_matches/len(self.a3_data)*100):.1f}%")
        print("="*60)

    def run_mapping_process(self) -> None:
        """
        Execute the complete SKU mapping process.

        This is the main method that orchestrates the entire mapping workflow:
        1. Load data from both files
        2. Perform EAN matching
        3. Perform composite SKU matching
        4. Generate output files
        """
        try:
            logger.info("Starting SKU mapping process...")

            # Step 1: Load data
            self.load_data()

            # Step 2: EAN matching
            ean_matches = self.perform_ean_matching()

            # Step 3: Composite SKU matching
            all_matches = self.perform_composite_sku_matching(ean_matches)

            # Step 4: Generate output
            self.generate_mapping_output(all_matches)

            logger.info("SKU mapping process completed successfully!")

        except Exception as e:
            logger.error(f"Error in mapping process: {e}")
            raise


def main():
    """
    Main function to run the SKU mapping process.

    This function initializes the SkuMapper with the correct file paths
    and executes the complete mapping workflow.
    """
    # File paths - update these to match your actual file locations
    a3_file_path = "a3_distribution_march_2025_Latest.xlsx"
    nielsen_file_path = "CARREFOUR (incl. Drive) Data Pull 3.xlsx"

    # Initialize and run the mapper
    mapper = SkuMapper(a3_file_path, nielsen_file_path)
    mapper.run_mapping_process()


if __name__ == "__main__":
    main()
